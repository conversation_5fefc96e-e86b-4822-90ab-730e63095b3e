# 文件筛选过滤工具 - 使用指南

## 🎉 恭喜！你的文件筛选工具已经创建完成！

这是一个功能强大的Python文件筛选和管理工具，支持多种筛选条件和操作方式。

## 📁 项目文件说明

- **`file_filter.py`** - 核心筛选逻辑类
- **`main.py`** - 命令行工具入口
- **`gui.py`** - 图形界面程序
- **`config.py`** - 配置文件管理
- **`demo.py`** - 功能演示脚本
- **`test_file_filter.py`** - 单元测试
- **`requirements.txt`** - 依赖包列表
- **`sample_config.json`** - 示例配置文件
- **`README.md`** - 详细说明文档

## 🚀 快速开始

### 1. 命令行使用

```bash
# 查找当前目录下的所有Python文件
python main.py --ext .py --list

# 查找大于1MB的文件并显示详细信息
python main.py --min-size 1MB --list --details

# 复制所有图片文件到images目录
python main.py --ext .jpg .png .gif --copy ./images/

# 删除7天前的日志文件
python main.py --ext .log --days 7 --delete
```

### 2. 图形界面使用

```bash
python gui.py
```

启动后你会看到一个直观的图形界面，可以：
- 选择搜索路径
- 设置各种筛选条件
- 预览搜索结果
- 执行复制、移动、删除操作
- 保存和加载配置

### 3. 运行演示

```bash
python demo.py
```

这会创建示例文件并演示所有功能。

### 4. 运行测试

```bash
python test_file_filter.py
```

验证所有功能是否正常工作。

## 🔧 主要功能

### 筛选条件
- ✅ **文件扩展名** - 如 `.txt`, `.py`, `.jpg`
- ✅ **文件大小** - 支持 B, KB, MB, GB 单位
- ✅ **修改时间** - 指定天数内修改的文件
- ✅ **文件名模式** - 支持正则表达式
- ✅ **递归搜索** - 搜索子目录

### 操作功能
- ✅ **列出文件** - 显示符合条件的文件
- ✅ **复制文件** - 复制到指定目录
- ✅ **移动文件** - 移动到指定目录
- ✅ **删除文件** - 安全删除（带确认）

### 使用方式
- ✅ **命令行工具** - 适合脚本和自动化
- ✅ **图形界面** - 直观易用
- ✅ **配置文件** - 保存常用筛选规则
- ✅ **Python API** - 集成到其他程序

## 📝 使用示例

### 常见任务

1. **清理临时文件**
```bash
python main.py --ext .tmp .temp --delete
```

2. **整理下载文件夹的图片**
```bash
python main.py --path ./Downloads --ext .jpg .png --move ./Pictures/
```

3. **查找占用空间的大文件**
```bash
python main.py --min-size 100MB --list --details
```

4. **备份最近修改的文档**
```bash
python main.py --ext .doc .docx .pdf --days 7 --copy ./backup/
```

### 高级用法

1. **使用正则表达式查找文件**
```bash
python main.py --name ".*backup.*" --list
```

2. **组合多个条件**
```bash
python main.py --ext .log --min-size 1MB --days 30 --delete
```

3. **使用配置文件**
```python
from config import ConfigManager
from file_filter import FileFilter

# 加载配置
config_manager = ConfigManager("sample_config.json")
config = config_manager.get_config("Python文件备份")

# 执行筛选
filter = FileFilter(config.base_path)
files = filter.find_files(extensions=config.extensions)
filter.copy_files(files, config.destination)
```

## ⚠️ 注意事项

1. **删除操作** - 删除前会要求确认，请谨慎操作
2. **文件冲突** - 复制/移动时如果目标文件存在，会自动重命名
3. **权限问题** - 确保对目标目录有读写权限
4. **大量文件** - 处理大量文件时请耐心等待

## 🛠️ 自定义和扩展

### 添加新的筛选条件
在 `file_filter.py` 的 `_match_criteria` 方法中添加新的判断逻辑。

### 添加新的操作
在 `FileFilter` 类中添加新的方法，参考现有的 `copy_files`, `move_files` 等。

### 修改界面
编辑 `gui.py` 文件来自定义图形界面。

## 🐛 故障排除

### 常见问题

1. **"找不到模块"错误**
   - 确保已安装Python 3.6+
   - 运行 `pip install -r requirements.txt` 安装依赖

2. **权限错误**
   - 在Windows上以管理员身份运行
   - 在Linux/Mac上使用 `sudo` 或检查文件权限

3. **GUI无法启动**
   - 确保系统支持tkinter（通常预装）
   - 在服务器环境中使用命令行版本

4. **配置文件错误**
   - 检查JSON格式是否正确
   - 使用示例配置文件作为模板

## 📞 获取帮助

- 查看 `README.md` 获取详细文档
- 运行 `python main.py --help` 查看命令行帮助
- 运行 `python demo.py` 查看功能演示
- 运行测试确保功能正常：`python test_file_filter.py`

## 🎯 下一步

1. 根据你的需求修改配置文件
2. 创建自己的筛选规则
3. 将工具集成到日常工作流程中
4. 考虑添加新功能或改进现有功能

祝你使用愉快！🎉
