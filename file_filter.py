#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选过滤器
支持多种筛选条件和操作
"""

import os
import shutil
import glob
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

class FileFilter:
    """文件筛选过滤器类"""
    
    def __init__(self, base_path: str = "."):
        """
        初始化文件筛选器
        
        Args:
            base_path: 基础搜索路径
        """
        self.base_path = Path(base_path).resolve()
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('FileFilter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def find_files(self, 
                   extensions: Optional[List[str]] = None,
                   name_pattern: Optional[str] = None,
                   min_size: Optional[int] = None,
                   max_size: Optional[int] = None,
                   modified_days: Optional[int] = None,
                   recursive: bool = True) -> List[Path]:
        """
        根据条件筛选文件
        
        Args:
            extensions: 文件扩展名列表，如 ['.txt', '.py']
            name_pattern: 文件名模式（支持通配符）
            min_size: 最小文件大小（字节）
            max_size: 最大文件大小（字节）
            modified_days: 修改时间（天数内）
            recursive: 是否递归搜索子目录
            
        Returns:
            符合条件的文件路径列表
        """
        files = []
        
        # 获取所有文件
        if recursive:
            pattern = "**/*"
        else:
            pattern = "*"
            
        for file_path in self.base_path.glob(pattern):
            if file_path.is_file():
                if self._match_criteria(file_path, extensions, name_pattern, 
                                      min_size, max_size, modified_days):
                    files.append(file_path)
                    
        self.logger.info(f"找到 {len(files)} 个符合条件的文件")
        return files
    
    def _match_criteria(self, file_path: Path, 
                       extensions: Optional[List[str]],
                       name_pattern: Optional[str],
                       min_size: Optional[int],
                       max_size: Optional[int],
                       modified_days: Optional[int]) -> bool:
        """检查文件是否符合筛选条件"""
        
        # 检查扩展名
        if extensions:
            if file_path.suffix.lower() not in [ext.lower() for ext in extensions]:
                return False
        
        # 检查文件名模式
        if name_pattern:
            if not re.search(name_pattern, file_path.name, re.IGNORECASE):
                return False
        
        # 检查文件大小
        try:
            file_size = file_path.stat().st_size
            if min_size and file_size < min_size:
                return False
            if max_size and file_size > max_size:
                return False
        except OSError:
            return False
        
        # 检查修改时间
        if modified_days:
            try:
                modified_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                cutoff_time = datetime.now() - timedelta(days=modified_days)
                if modified_time < cutoff_time:
                    return False
            except OSError:
                return False
        
        return True
    
    def list_files(self, files: List[Path], show_details: bool = False) -> None:
        """列出文件信息"""
        if not files:
            print("没有找到符合条件的文件")
            return
            
        print(f"\n找到 {len(files)} 个文件:")
        print("-" * 80)
        
        for file_path in files:
            if show_details:
                try:
                    stat = file_path.stat()
                    size = self._format_size(stat.st_size)
                    modified = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                    print(f"{file_path.relative_to(self.base_path)} | {size} | {modified}")
                except OSError:
                    print(f"{file_path.relative_to(self.base_path)} | 无法获取详细信息")
            else:
                print(file_path.relative_to(self.base_path))
    
    def copy_files(self, files: List[Path], destination: str) -> None:
        """复制文件到目标目录"""
        dest_path = Path(destination)
        dest_path.mkdir(parents=True, exist_ok=True)
        
        success_count = 0
        for file_path in files:
            try:
                dest_file = dest_path / file_path.name
                # 如果目标文件已存在，添加数字后缀
                counter = 1
                while dest_file.exists():
                    name_parts = file_path.stem, counter, file_path.suffix
                    dest_file = dest_path / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                    counter += 1
                
                shutil.copy2(file_path, dest_file)
                success_count += 1
                self.logger.info(f"复制: {file_path} -> {dest_file}")
            except Exception as e:
                self.logger.error(f"复制失败 {file_path}: {e}")
        
        print(f"成功复制 {success_count}/{len(files)} 个文件到 {destination}")
    
    def move_files(self, files: List[Path], destination: str) -> None:
        """移动文件到目标目录"""
        dest_path = Path(destination)
        dest_path.mkdir(parents=True, exist_ok=True)
        
        success_count = 0
        for file_path in files:
            try:
                dest_file = dest_path / file_path.name
                # 如果目标文件已存在，添加数字后缀
                counter = 1
                while dest_file.exists():
                    name_parts = file_path.stem, counter, file_path.suffix
                    dest_file = dest_path / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                    counter += 1
                
                shutil.move(str(file_path), str(dest_file))
                success_count += 1
                self.logger.info(f"移动: {file_path} -> {dest_file}")
            except Exception as e:
                self.logger.error(f"移动失败 {file_path}: {e}")
        
        print(f"成功移动 {success_count}/{len(files)} 个文件到 {destination}")
    
    def delete_files(self, files: List[Path], confirm: bool = True) -> None:
        """删除文件"""
        if not files:
            print("没有文件需要删除")
            return
        
        if confirm:
            print(f"即将删除 {len(files)} 个文件:")
            for file_path in files[:5]:  # 只显示前5个
                print(f"  {file_path.relative_to(self.base_path)}")
            if len(files) > 5:
                print(f"  ... 还有 {len(files) - 5} 个文件")
            
            response = input("\n确认删除这些文件吗? (y/N): ")
            if response.lower() != 'y':
                print("取消删除操作")
                return
        
        success_count = 0
        for file_path in files:
            try:
                file_path.unlink()
                success_count += 1
                self.logger.info(f"删除: {file_path}")
            except Exception as e:
                self.logger.error(f"删除失败 {file_path}: {e}")
        
        print(f"成功删除 {success_count}/{len(files)} 个文件")
    
    def _format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
