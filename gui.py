#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具 - 图形界面
使用 tkinter 创建简单的GUI界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
import threading
from file_filter import FileFilter
from config import ConfigManager, FilterConfig

class FileFilterGUI:
    """文件筛选工具图形界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("文件筛选过滤工具")
        self.root.geometry("800x600")
        
        self.file_filter = None
        self.config_manager = ConfigManager()
        self.found_files = []
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 路径选择
        ttk.Label(main_frame, text="搜索路径:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.path_var = tk.StringVar(value=".")
        path_frame = ttk.Frame(main_frame)
        path_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        path_frame.columnconfigure(0, weight=1)
        
        ttk.Entry(path_frame, textvariable=self.path_var).grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(path_frame, text="浏览", command=self.browse_path).grid(row=0, column=1)
        
        # 递归搜索选项
        self.recursive_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(main_frame, text="递归搜索子目录", variable=self.recursive_var).grid(row=1, column=1, sticky=tk.W, pady=2)
        
        # 筛选条件框架
        filter_frame = ttk.LabelFrame(main_frame, text="筛选条件", padding="5")
        filter_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        filter_frame.columnconfigure(1, weight=1)
        
        # 文件扩展名
        ttk.Label(filter_frame, text="文件扩展名:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.extensions_var = tk.StringVar()
        ttk.Entry(filter_frame, textvariable=self.extensions_var, 
                 width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        ttk.Label(filter_frame, text="(如: .txt .py .jpg)", font=('TkDefaultFont', 8)).grid(row=0, column=2, sticky=tk.W, padx=(5, 0))
        
        # 文件名模式
        ttk.Label(filter_frame, text="文件名模式:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.pattern_var = tk.StringVar()
        ttk.Entry(filter_frame, textvariable=self.pattern_var).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        ttk.Label(filter_frame, text="(支持正则表达式)", font=('TkDefaultFont', 8)).grid(row=1, column=2, sticky=tk.W, padx=(5, 0))
        
        # 文件大小
        size_frame = ttk.Frame(filter_frame)
        size_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        size_frame.columnconfigure(1, weight=1)
        size_frame.columnconfigure(3, weight=1)
        
        ttk.Label(filter_frame, text="文件大小:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Label(size_frame, text="最小:").grid(row=0, column=0, sticky=tk.W)
        self.min_size_var = tk.StringVar()
        ttk.Entry(size_frame, textvariable=self.min_size_var, width=15).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(2, 5))
        ttk.Label(size_frame, text="最大:").grid(row=0, column=2, sticky=tk.W)
        self.max_size_var = tk.StringVar()
        ttk.Entry(size_frame, textvariable=self.max_size_var, width=15).grid(row=0, column=3, sticky=(tk.W, tk.E), padx=(2, 0))
        
        # 修改时间
        ttk.Label(filter_frame, text="修改时间:").grid(row=3, column=0, sticky=tk.W, pady=2)
        time_frame = ttk.Frame(filter_frame)
        time_frame.grid(row=3, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        self.days_var = tk.StringVar()
        ttk.Entry(time_frame, textvariable=self.days_var, width=10).grid(row=0, column=0)
        ttk.Label(time_frame, text="天内修改").grid(row=0, column=1, sticky=tk.W, padx=(2, 0))
        
        # 操作按钮框架
        action_frame = ttk.LabelFrame(main_frame, text="操作", padding="5")
        action_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        button_frame = ttk.Frame(action_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="搜索文件", command=self.search_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="复制到...", command=self.copy_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="移动到...", command=self.move_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="删除文件", command=self.delete_files).pack(side=tk.LEFT, padx=(0, 5))
        
        # 配置管理按钮
        config_frame = ttk.Frame(action_frame)
        config_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(config_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(config_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=(0, 5))
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="搜索结果", padding="5")
        result_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 创建树形视图显示文件列表
        columns = ('文件名', '大小', '修改时间', '路径')
        self.tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150)
        
        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(result_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
    
    def browse_path(self):
        """浏览选择路径"""
        path = filedialog.askdirectory(initialdir=self.path_var.get())
        if path:
            self.path_var.set(path)
    
    def parse_size(self, size_str):
        """解析大小字符串"""
        if not size_str.strip():
            return None
        
        size_str = size_str.upper().strip()
        multipliers = {'B': 1, 'KB': 1024, 'MB': 1024**2, 'GB': 1024**3}
        
        for unit, multiplier in multipliers.items():
            if size_str.endswith(unit):
                try:
                    return int(float(size_str[:-len(unit)]) * multiplier)
                except ValueError:
                    return None
        
        try:
            return int(size_str)
        except ValueError:
            return None
    
    def search_files(self):
        """搜索文件"""
        def search_thread():
            try:
                self.status_var.set("正在搜索文件...")
                self.root.update()
                
                # 创建文件筛选器
                self.file_filter = FileFilter(self.path_var.get())
                
                # 解析参数
                extensions = None
                if self.extensions_var.get().strip():
                    extensions = [ext.strip() for ext in self.extensions_var.get().split()]
                
                pattern = self.pattern_var.get().strip() or None
                min_size = self.parse_size(self.min_size_var.get())
                max_size = self.parse_size(self.max_size_var.get())
                
                days = None
                if self.days_var.get().strip():
                    try:
                        days = int(self.days_var.get())
                    except ValueError:
                        pass
                
                # 搜索文件
                self.found_files = self.file_filter.find_files(
                    extensions=extensions,
                    name_pattern=pattern,
                    min_size=min_size,
                    max_size=max_size,
                    modified_days=days,
                    recursive=self.recursive_var.get()
                )
                
                # 更新界面
                self.root.after(0, self.update_results)
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"搜索失败: {e}"))
                self.root.after(0, lambda: self.status_var.set("搜索失败"))
        
        threading.Thread(target=search_thread, daemon=True).start()
    
    def update_results(self):
        """更新搜索结果显示"""
        # 清空现有结果
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新结果
        for file_path in self.found_files:
            try:
                stat = file_path.stat()
                size = self.format_size(stat.st_size)
                modified = self.format_time(stat.st_mtime)
                relative_path = file_path.relative_to(Path(self.path_var.get()))
                
                self.tree.insert('', tk.END, values=(
                    file_path.name,
                    size,
                    modified,
                    str(relative_path.parent) if str(relative_path.parent) != '.' else ''
                ))
            except Exception:
                self.tree.insert('', tk.END, values=(
                    file_path.name,
                    '未知',
                    '未知',
                    str(file_path.parent)
                ))
        
        self.status_var.set(f"找到 {len(self.found_files)} 个文件")
    
    def format_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def format_time(self, timestamp):
        """格式化时间"""
        from datetime import datetime
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M')
    
    def copy_files(self):
        """复制文件"""
        if not self.found_files:
            messagebox.showwarning("警告", "请先搜索文件")
            return
        
        dest = filedialog.askdirectory(title="选择目标目录")
        if dest:
            try:
                self.file_filter.copy_files(self.found_files, dest)
                messagebox.showinfo("成功", f"文件已复制到 {dest}")
            except Exception as e:
                messagebox.showerror("错误", f"复制失败: {e}")
    
    def move_files(self):
        """移动文件"""
        if not self.found_files:
            messagebox.showwarning("警告", "请先搜索文件")
            return
        
        dest = filedialog.askdirectory(title="选择目标目录")
        if dest:
            try:
                self.file_filter.move_files(self.found_files, dest)
                messagebox.showinfo("成功", f"文件已移动到 {dest}")
                self.search_files()  # 重新搜索以更新结果
            except Exception as e:
                messagebox.showerror("错误", f"移动失败: {e}")
    
    def delete_files(self):
        """删除文件"""
        if not self.found_files:
            messagebox.showwarning("警告", "请先搜索文件")
            return
        
        if messagebox.askyesno("确认", f"确定要删除 {len(self.found_files)} 个文件吗？"):
            try:
                self.file_filter.delete_files(self.found_files, confirm=False)
                messagebox.showinfo("成功", "文件已删除")
                self.search_files()  # 重新搜索以更新结果
            except Exception as e:
                messagebox.showerror("错误", f"删除失败: {e}")
    
    def save_config(self):
        """保存当前配置"""
        import tkinter.simpledialog
        name = tkinter.simpledialog.askstring("保存配置", "请输入配置名称:")
        if name:
            config = FilterConfig(
                name=name,
                base_path=self.path_var.get(),
                recursive=self.recursive_var.get(),
                extensions=self.extensions_var.get().split() if self.extensions_var.get().strip() else None,
                name_pattern=self.pattern_var.get() or None,
                min_size=self.parse_size(self.min_size_var.get()),
                max_size=self.parse_size(self.max_size_var.get()),
                modified_days=int(self.days_var.get()) if self.days_var.get().strip() else None
            )
            self.config_manager.add_config(config)
            messagebox.showinfo("成功", f"配置 '{name}' 已保存")
    
    def load_config(self):
        """加载配置"""
        configs = self.config_manager.list_configs()
        if not configs:
            messagebox.showinfo("提示", "没有保存的配置")
            return

        # 创建选择对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("选择配置")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text="选择要加载的配置:").pack(pady=10)

        listbox = tk.Listbox(dialog)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        for config_name in configs:
            listbox.insert(tk.END, config_name)

        def load_selected():
            selection = listbox.curselection()
            if selection:
                config_name = configs[selection[0]]
                config = self.config_manager.get_config(config_name)
                if config:
                    self.load_config_values(config)
                    dialog.destroy()
                    messagebox.showinfo("成功", f"配置 '{config_name}' 已加载")

        ttk.Button(dialog, text="加载", command=load_selected).pack(pady=5)
        ttk.Button(dialog, text="取消", command=dialog.destroy).pack()
    
    def load_config_values(self, config):
        """加载配置值到界面"""
        self.path_var.set(config.base_path)
        self.recursive_var.set(config.recursive)
        self.extensions_var.set(' '.join(config.extensions) if config.extensions else '')
        self.pattern_var.set(config.name_pattern or '')
        self.min_size_var.set(str(config.min_size) if config.min_size else '')
        self.max_size_var.set(str(config.max_size) if config.max_size else '')
        self.days_var.set(str(config.modified_days) if config.modified_days else '')

def main():
    root = tk.Tk()
    app = FileFilterGUI(root)
    root.mainloop()

if __name__ == '__main__':
    main()
