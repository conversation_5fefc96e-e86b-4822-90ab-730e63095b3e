#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具 - GUI启动器
自动检测并启动最佳的图形界面版本
"""

import sys
import os

def main():
    """主函数 - 启动GUI"""
    print("文件筛选过滤工具 - 图形界面")
    print("=" * 40)
    
    # 首先尝试PySide6版本
    try:
        print("正在启动PySide6版本...")
        from gui_pyside6 import main as pyside6_main
        pyside6_main()
        return
    except ImportError as e:
        print(f"PySide6不可用: {e}")
        print("尝试安装: pip install PySide6")
    except Exception as e:
        print(f"PySide6版本启动失败: {e}")
    
    # 如果PySide6不可用，尝试tkinter版本
    try:
        print("正在启动tkinter版本...")
        from gui import main as tkinter_main
        tkinter_main()
        return
    except ImportError as e:
        print(f"tkinter不可用: {e}")
    except Exception as e:
        print(f"tkinter版本启动失败: {e}")
    
    # 如果都不可用，显示错误信息
    print("\n错误: 无法启动图形界面")
    print("请确保安装了以下任一GUI库:")
    print("1. PySide6 (推荐): pip install PySide6")
    print("2. tkinter (通常预装在Python中)")
    print("\n或者使用命令行版本: python main.py --help")
    
    input("\n按回车键退出...")

if __name__ == '__main__':
    main()
