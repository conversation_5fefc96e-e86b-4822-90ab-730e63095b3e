# PySide6图形界面说明

## 🎨 界面特性

### 现代化设计
- 基于Qt6框架，界面美观现代
- 支持Fusion样式，在不同操作系统上保持一致的外观
- 使用图标和颜色区分不同功能按钮
- 表格支持排序、交替行颜色等现代特性

### 响应式布局
- 使用分割器(QSplitter)实现可调整的界面布局
- 表格列宽自动调整，适应不同内容长度
- 窗口可以自由缩放，最小尺寸1000x700

### 多线程支持
- 文件搜索在后台线程执行，界面不会卡顿
- 文件操作（复制、移动、删除）也在后台执行
- 实时显示操作进度和状态信息

## 🖥️ 界面布局

### 上半部分：搜索条件设置
1. **搜索路径组**
   - 路径输入框：手动输入或粘贴路径
   - 浏览按钮：打开文件夹选择对话框
   - 递归搜索复选框：是否搜索子目录

2. **筛选条件组**
   - 文件扩展名：支持多个扩展名，空格分隔
   - 文件名模式：支持正则表达式匹配
   - 文件大小：最小值和最大值，支持B/KB/MB/GB单位
   - 修改时间：指定天数内修改的文件

3. **操作按钮组**
   - 🔍 搜索文件：绿色按钮，开始搜索
   - 📋 复制到...：复制找到的文件
   - 📁 移动到...：移动找到的文件
   - 🗑️ 删除文件：红色按钮，删除文件

4. **配置管理组**
   - 💾 保存配置：保存当前筛选条件
   - 📂 加载配置：加载已保存的配置

### 下半部分：搜索结果显示
- **结果表格**：显示找到的文件信息
  - 文件名：文件的名称
  - 大小：格式化的文件大小
  - 修改时间：最后修改时间
  - 路径：文件所在的相对路径
- **表格功能**：
  - 点击列标题可排序
  - 支持多选（Ctrl+点击）
  - 交替行颜色便于阅读

### 菜单栏
- **文件菜单**：退出程序
- **工具菜单**：运行演示程序
- **帮助菜单**：显示关于信息

### 状态栏
- 显示当前操作状态
- 显示找到的文件数量
- 操作时显示进度条

## 🚀 使用流程

### 基本搜索流程
1. **设置搜索路径**
   - 在路径输入框中输入要搜索的目录
   - 或点击"浏览..."按钮选择目录
   - 勾选"递归搜索子目录"以搜索所有子文件夹

2. **设置筛选条件**
   - 文件扩展名：如输入".txt .py .jpg"
   - 文件名模式：如输入".*log.*"匹配包含log的文件
   - 文件大小：如最小值"1MB"，最大值"100MB"
   - 修改时间：如输入"7"表示最近7天内修改的文件

3. **执行搜索**
   - 点击"🔍 搜索文件"按钮
   - 状态栏显示"正在搜索文件..."
   - 搜索完成后结果显示在下方表格中

4. **查看结果**
   - 表格显示所有找到的文件
   - 可以点击列标题进行排序
   - 状态栏显示找到的文件总数

5. **执行操作**
   - 📋 复制：选择目标文件夹，复制所有找到的文件
   - 📁 移动：选择目标文件夹，移动所有找到的文件
   - 🗑️删除：确认后删除所有找到的文件

### 配置管理流程
1. **保存配置**
   - 设置好筛选条件后
   - 点击"💾 保存配置"按钮
   - 输入配置名称并保存

2. **加载配置**
   - 点击"📂 加载配置"按钮
   - 从列表中选择要加载的配置
   - 配置会自动填入界面

3. **管理配置**
   - 在配置对话框中可以删除不需要的配置
   - 双击配置名称可以快速加载

## ⚡ 高级功能

### 多线程操作
- 所有耗时操作都在后台线程执行
- 界面始终保持响应，可以随时取消操作
- 进度条实时显示操作进度

### 智能文件处理
- 复制/移动时自动处理文件名冲突
- 如果目标文件已存在，自动添加数字后缀
- 操作完成后自动刷新搜索结果

### 错误处理
- 完善的错误提示和处理机制
- 操作失败时显示详细错误信息
- 支持操作撤销（对于移动和删除操作）

### 快捷键支持
- Ctrl+Q：退出程序
- 支持标准的表格操作快捷键

## 🎯 使用技巧

### 高效搜索
1. **组合条件**：同时使用多个筛选条件缩小搜索范围
2. **正则表达式**：使用强大的模式匹配功能
3. **大小筛选**：快速找到占用空间大的文件
4. **时间筛选**：找到最近修改或很久未修改的文件

### 批量操作
1. **预览结果**：操作前先查看搜索结果
2. **分批处理**：对于大量文件，可以分批进行操作
3. **备份重要文件**：删除前先复制到备份目录

### 配置管理
1. **常用配置**：为常用的筛选条件创建配置
2. **任务导向**：为不同任务创建专门的配置
3. **定期清理**：删除不再使用的配置

## 🔧 故障排除

### 常见问题
1. **界面无法启动**
   - 确保已安装PySide6：`pip install PySide6`
   - 检查Python版本是否支持（需要3.6+）

2. **搜索速度慢**
   - 缩小搜索范围
   - 使用更具体的筛选条件
   - 避免在网络驱动器上搜索

3. **操作失败**
   - 检查目标目录权限
   - 确保有足够的磁盘空间
   - 检查文件是否被其他程序占用

### 性能优化
1. **合理使用递归搜索**
2. **设置合适的文件大小范围**
3. **使用具体的扩展名筛选**
4. **定期清理搜索历史**

这个PySide6版本的界面提供了更好的用户体验和更强大的功能，是推荐使用的版本！
