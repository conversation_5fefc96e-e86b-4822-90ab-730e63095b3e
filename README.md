# 文件筛选过滤工具

一个功能强大的Python文件筛选和管理工具，支持多种筛选条件和操作方式。

## 功能特性

- 🔍 **多种筛选条件**
  - 文件扩展名筛选
  - 文件大小范围筛选
  - 修改时间筛选
  - 文件名模式匹配（支持正则表达式）
  - 递归搜索子目录

- 🛠️ **多种操作方式**
  - 列出符合条件的文件
  - 复制文件到指定目录
  - 移动文件到指定目录
  - 删除符合条件的文件

- 💻 **多种使用方式**
  - 命令行工具
  - 现代化图形界面（PySide6/Qt6）
  - 传统图形界面（tkinter）
  - 配置文件管理
  - Python API

## 安装

1. 克隆或下载项目文件
2. 安装依赖包：

```bash
# 安装基础依赖
pip install -r requirements.txt

# 或者只安装PySide6（推荐的GUI库）
pip install PySide6
```

## 使用方法

### 命令行使用

#### 基本语法
```bash
python main.py [选项] --操作 [参数]
```

#### 常用示例

1. **列出当前目录下的所有Python文件**
```bash
python main.py --ext .py --list
```

2. **查找大于10MB的文件**
```bash
python main.py --min-size 10MB --list --details
```

3. **删除7天前的日志文件**
```bash
python main.py --ext .log --days 7 --delete
```

4. **复制所有图片文件到images目录**
```bash
python main.py --ext .jpg .png .gif --copy ./images/
```

5. **移动小于1KB的文件到small_files目录**
```bash
python main.py --max-size 1KB --move ./small_files/
```

6. **递归搜索包含"temp"的文件**
```bash
python main.py --name ".*temp.*" --recursive --list
```

#### 命令行参数说明

**基本参数：**
- `--path, -p`: 搜索路径（默认：当前目录）
- `--recursive, -r`: 递归搜索子目录

**筛选条件：**
- `--ext`: 文件扩展名（如：.txt .py .jpg）
- `--name`: 文件名模式（支持正则表达式）
- `--min-size`: 最小文件大小（如：1KB, 10MB）
- `--max-size`: 最大文件大小（如：100MB, 1GB）
- `--days`: 修改时间在指定天数内

**操作选项：**
- `--list, -l`: 列出符合条件的文件
- `--copy DEST`: 复制文件到指定目录
- `--move DEST`: 移动文件到指定目录
- `--delete`: 删除符合条件的文件

**其他选项：**
- `--details, -d`: 显示文件详细信息（仅用于--list）
- `--no-confirm`: 删除文件时不需要确认
- `--verbose, -v`: 显示详细日志

### 图形界面使用

#### 方式一：自动启动最佳GUI版本
```bash
python 启动GUI.py
```

#### 方式二：直接启动PySide6版本（推荐）
```bash
python gui_pyside6.py
```

#### 方式三：启动tkinter版本
```bash
python gui.py
```

**PySide6版本特性：**
- 现代化的Qt6界面设计
- 更好的性能和响应速度
- 支持多线程操作，界面不会卡顿
- 更丰富的界面元素和交互
- 支持菜单栏和状态栏
- 内置进度条显示操作进度

**GUI界面操作步骤：**
1. 选择搜索路径（可点击"浏览"按钮）
2. 设置筛选条件（扩展名、大小、时间等）
3. 点击"🔍 搜索文件"查看结果
4. 在结果表格中查看找到的文件
5. 选择相应的操作（📋 复制、📁 移动、🗑️ 删除）
6. 可以保存和加载常用的筛选配置

### 配置文件使用

#### 创建示例配置
```bash
python config.py
```

这会创建一个 `sample_config.json` 文件，包含多个预设的筛选配置。

#### 配置文件格式示例
```json
{
  "清理日志文件": {
    "name": "清理日志文件",
    "base_path": ".",
    "recursive": true,
    "extensions": [".log", ".tmp"],
    "modified_days": 30,
    "action": "delete",
    "confirm_delete": true
  },
  "图片文件整理": {
    "name": "图片文件整理",
    "base_path": ".",
    "recursive": true,
    "extensions": [".jpg", ".jpeg", ".png", ".gif"],
    "action": "copy",
    "destination": "./images/"
  }
}
```

### Python API 使用

```python
from file_filter import FileFilter

# 创建文件筛选器
filter = FileFilter("/path/to/search")

# 查找文件
files = filter.find_files(
    extensions=['.py', '.txt'],
    min_size=1024,  # 1KB
    modified_days=7
)

# 列出文件
filter.list_files(files, show_details=True)

# 复制文件
filter.copy_files(files, "/path/to/destination")
```

## 文件大小格式

支持以下大小单位：
- `B`: 字节
- `KB`: 千字节
- `MB`: 兆字节
- `GB`: 吉字节

示例：`100B`, `1.5KB`, `10MB`, `2GB`

## 注意事项

1. **删除操作**：删除文件前会要求确认，使用 `--no-confirm` 可跳过确认
2. **文件冲突**：复制或移动文件时，如果目标文件已存在，会自动添加数字后缀
3. **权限问题**：确保对目标目录有读写权限
4. **大文件操作**：处理大量文件时可能需要一些时间，请耐心等待

## 项目结构

```
file-filter/
├── file_filter.py      # 核心筛选逻辑
├── main.py            # 命令行入口
├── gui_pyside6.py     # PySide6图形界面（推荐）
├── gui.py             # tkinter图形界面
├── 启动GUI.py          # GUI启动器
├── config.py          # 配置管理
├── demo.py            # 功能演示
├── test_file_filter.py # 单元测试
├── requirements.txt   # 依赖包列表
├── sample_config.json # 示例配置文件
├── README.md          # 详细说明文档
└── 使用指南.md        # 快速使用指南
```

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具！
