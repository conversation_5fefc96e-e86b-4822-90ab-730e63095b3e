#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具演示脚本
创建示例文件并演示各种筛选功能
"""

import os
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
import time

from file_filter import FileFilter

def create_demo_files(demo_dir):
    """创建演示文件"""
    print(f"在 {demo_dir} 创建演示文件...")
    
    # 创建各种类型的文件
    files_to_create = [
        # 文本文件
        ('readme.txt', 'This is a readme file\nWith multiple lines'),
        ('notes.txt', 'Some notes here'),
        ('config.txt', 'Configuration settings'),
        
        # Python文件
        ('main.py', 'print("Hello World")'),
        ('utils.py', 'def helper_function():\n    pass'),
        ('test_script.py', 'import unittest\nclass TestCase(unittest.TestCase):\n    pass'),
        
        # 日志文件
        ('app.log', 'INFO: Application started\nERROR: Something went wrong'),
        ('debug.log', 'DEBUG: Detailed information'),
        ('access.log', '127.0.0.1 - - [01/Jan/2024:00:00:00] "GET /" 200'),
        
        # 临时文件
        ('temp1.tmp', 'temporary data 1'),
        ('temp2.tmp', 'temporary data 2'),
        ('cache.tmp', 'cached information'),
        
        # 图片文件（模拟）
        ('photo1.jpg', 'fake jpeg data' * 100),  # 较大文件
        ('photo2.jpg', 'small jpeg'),
        ('icon.png', 'png data'),
        
        # 文档文件
        ('document.pdf', 'fake pdf content' * 50),
        ('report.docx', 'document content'),
        
        # 其他文件
        ('data.json', '{"key": "value", "number": 42}'),
        ('style.css', 'body { margin: 0; padding: 0; }'),
        ('script.js', 'console.log("Hello JavaScript");'),
    ]
    
    # 创建主目录文件
    for filename, content in files_to_create:
        file_path = demo_dir / filename
        file_path.write_text(content, encoding='utf-8')
    
    # 创建子目录结构
    subdirs = ['logs', 'images', 'docs', 'backup']
    for subdir in subdirs:
        (demo_dir / subdir).mkdir(exist_ok=True)
    
    # 在子目录中创建文件
    (demo_dir / 'logs' / 'system.log').write_text('System log entries')
    (demo_dir / 'logs' / 'error.log').write_text('Error log entries')
    (demo_dir / 'images' / 'banner.jpg').write_text('banner image data' * 200)
    (demo_dir / 'images' / 'logo.png').write_text('logo image data')
    (demo_dir / 'docs' / 'manual.pdf').write_text('user manual content' * 100)
    (demo_dir / 'docs' / 'guide.txt').write_text('user guide')
    (demo_dir / 'backup' / 'old_config.bak').write_text('old configuration')
    (demo_dir / 'backup' / 'data_backup.sql').write_text('database backup')
    
    # 创建一些旧文件（修改时间为几天前）
    old_files = [
        demo_dir / 'old_readme.txt',
        demo_dir / 'backup' / 'very_old.bak'
    ]
    
    for old_file in old_files:
        old_file.write_text('old file content')
        # 设置修改时间为5天前
        old_time = time.time() - (5 * 24 * 60 * 60)
        os.utime(str(old_file), (old_time, old_time))
    
    print(f"已创建 {len(files_to_create) + 8} 个演示文件")

def demo_basic_filtering(file_filter):
    """演示基本筛选功能"""
    print("\n" + "="*60)
    print("基本筛选功能演示")
    print("="*60)
    
    # 1. 按扩展名筛选
    print("\n1. 查找所有Python文件:")
    files = file_filter.find_files(extensions=['.py'])
    file_filter.list_files(files)
    
    # 2. 按文件大小筛选
    print("\n2. 查找大于500字节的文件:")
    files = file_filter.find_files(min_size=500)
    file_filter.list_files(files, show_details=True)
    
    # 3. 按文件名模式筛选
    print("\n3. 查找包含'log'的文件:")
    files = file_filter.find_files(name_pattern=r'.*log.*')
    file_filter.list_files(files)
    
    # 4. 按修改时间筛选
    print("\n4. 查找最近3天内修改的文件:")
    files = file_filter.find_files(modified_days=3)
    file_filter.list_files(files)

def demo_advanced_filtering(file_filter):
    """演示高级筛选功能"""
    print("\n" + "="*60)
    print("高级筛选功能演示")
    print("="*60)
    
    # 1. 组合条件筛选
    print("\n1. 查找大于100字节的图片文件:")
    files = file_filter.find_files(
        extensions=['.jpg', '.png'],
        min_size=100
    )
    file_filter.list_files(files, show_details=True)
    
    # 2. 递归搜索
    print("\n2. 递归查找所有日志文件:")
    files = file_filter.find_files(
        extensions=['.log'],
        recursive=True
    )
    file_filter.list_files(files)
    
    # 3. 复杂模式匹配
    print("\n3. 查找以'temp'或'cache'开头的文件:")
    files = file_filter.find_files(
        name_pattern=r'^(temp|cache).*'
    )
    file_filter.list_files(files)

def demo_file_operations(file_filter, demo_dir):
    """演示文件操作功能"""
    print("\n" + "="*60)
    print("文件操作功能演示")
    print("="*60)
    
    # 1. 复制操作
    print("\n1. 复制所有Python文件到backup目录:")
    files = file_filter.find_files(extensions=['.py'])
    backup_dir = demo_dir / 'python_backup'
    file_filter.copy_files(files, str(backup_dir))
    
    # 验证复制结果
    print("复制后的文件:")
    for file in backup_dir.glob('*'):
        print(f"  {file.name}")
    
    # 2. 移动操作
    print("\n2. 移动所有临时文件到temp目录:")
    files = file_filter.find_files(extensions=['.tmp'])
    temp_dir = demo_dir / 'temp_files'
    if files:
        file_filter.move_files(files, str(temp_dir))
        
        print("移动后temp目录的文件:")
        for file in temp_dir.glob('*'):
            print(f"  {file.name}")
    
    # 3. 删除操作（演示，但不实际删除）
    print("\n3. 查找可以删除的旧文件:")
    files = file_filter.find_files(modified_days=10)  # 10天前的文件
    old_files = []
    for file in files:
        try:
            # 检查是否是真正的旧文件
            mtime = file.stat().st_mtime
            age_days = (time.time() - mtime) / (24 * 60 * 60)
            if age_days > 4:  # 4天前的文件
                old_files.append(file)
        except:
            pass
    
    if old_files:
        print("找到以下旧文件（演示，不会实际删除）:")
        for file in old_files:
            print(f"  {file.name}")
    else:
        print("没有找到旧文件")

def main():
    """主演示函数"""
    print("文件筛选工具演示")
    print("="*60)
    
    # 创建临时演示目录
    demo_dir = Path(tempfile.mkdtemp(prefix='file_filter_demo_'))
    print(f"演示目录: {demo_dir}")
    
    try:
        # 创建演示文件
        create_demo_files(demo_dir)
        
        # 创建文件筛选器
        file_filter = FileFilter(str(demo_dir))
        
        # 演示各种功能
        demo_basic_filtering(file_filter)
        demo_advanced_filtering(file_filter)
        demo_file_operations(file_filter, demo_dir)
        
        print("\n" + "="*60)
        print("演示完成！")
        print(f"演示文件保存在: {demo_dir}")
        print("你可以手动查看这些文件，或者运行以下命令进行测试:")
        print(f"python main.py --path \"{demo_dir}\" --ext .py --list")
        print(f"python main.py --path \"{demo_dir}\" --min-size 500 --list --details")
        
        # 询问是否保留演示文件
        keep_files = input("\n是否保留演示文件？(y/N): ").lower().strip()
        if keep_files != 'y':
            shutil.rmtree(str(demo_dir))
            print("演示文件已删除")
        else:
            print(f"演示文件保留在: {demo_dir}")
            
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        # 清理临时文件
        if demo_dir.exists():
            shutil.rmtree(str(demo_dir))

if __name__ == '__main__':
    main()
