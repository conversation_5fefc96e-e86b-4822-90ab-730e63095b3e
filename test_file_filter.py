#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具测试
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta
import os
import time

from file_filter import FileFilter

class TestFileFilter(unittest.TestCase):
    """文件筛选器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时测试目录
        self.test_dir = Path(tempfile.mkdtemp())
        self.filter = FileFilter(str(self.test_dir))
        
        # 创建测试文件
        self.create_test_files()
    
    def tearDown(self):
        """测试后清理"""
        # 删除临时测试目录
        shutil.rmtree(str(self.test_dir))
    
    def create_test_files(self):
        """创建测试文件"""
        # 创建不同类型的文件
        test_files = [
            ('test1.txt', 'Hello World'),
            ('test2.py', 'print("Hello")'),
            ('image.jpg', b'\xff\xd8\xff\xe0'),  # JPEG header
            ('document.pdf', b'%PDF-1.4'),
            ('large_file.dat', 'x' * 1024 * 1024),  # 1MB file
            ('small_file.tmp', 'small'),
        ]
        
        for filename, content in test_files:
            file_path = self.test_dir / filename
            if isinstance(content, str):
                file_path.write_text(content, encoding='utf-8')
            else:
                file_path.write_bytes(content)
        
        # 创建子目录和文件
        sub_dir = self.test_dir / 'subdir'
        sub_dir.mkdir()
        (sub_dir / 'nested.log').write_text('log content')
        (sub_dir / 'config.ini').write_text('[section]\nkey=value')
        
        # 创建一个旧文件（修改时间为7天前）
        old_file = self.test_dir / 'old_file.bak'
        old_file.write_text('old content')
        
        # 修改文件的修改时间
        old_time = time.time() - (7 * 24 * 60 * 60)  # 7天前
        os.utime(str(old_file), (old_time, old_time))
    
    def test_find_by_extension(self):
        """测试按扩展名筛选"""
        # 查找Python文件
        files = self.filter.find_files(extensions=['.py'])
        self.assertEqual(len(files), 1)
        self.assertTrue(any(f.name == 'test2.py' for f in files))
        
        # 查找多种扩展名
        files = self.filter.find_files(extensions=['.txt', '.py'])
        self.assertEqual(len(files), 2)
    
    def test_find_by_size(self):
        """测试按文件大小筛选"""
        # 查找大文件（>100KB）
        files = self.filter.find_files(min_size=100 * 1024)
        self.assertEqual(len(files), 1)
        self.assertTrue(any(f.name == 'large_file.dat' for f in files))
        
        # 查找小文件（<100字节）
        files = self.filter.find_files(max_size=100)
        small_files = [f.name for f in files]
        self.assertIn('test1.txt', small_files)
        self.assertIn('small_file.tmp', small_files)
    
    def test_find_by_name_pattern(self):
        """测试按文件名模式筛选"""
        # 查找包含"test"的文件
        files = self.filter.find_files(name_pattern=r'.*test.*')
        self.assertEqual(len(files), 2)
        
        # 查找以"image"开头的文件
        files = self.filter.find_files(name_pattern=r'^image.*')
        self.assertEqual(len(files), 1)
        self.assertTrue(any(f.name == 'image.jpg' for f in files))
    
    def test_find_by_modified_time(self):
        """测试按修改时间筛选"""
        # 查找最近1天内修改的文件
        files = self.filter.find_files(modified_days=1)
        # 应该找到除了old_file.bak之外的所有文件
        file_names = [f.name for f in files]
        self.assertNotIn('old_file.bak', file_names)
        self.assertIn('test1.txt', file_names)
    
    def test_recursive_search(self):
        """测试递归搜索"""
        # 递归搜索
        files = self.filter.find_files(recursive=True)
        file_names = [f.name for f in files]
        self.assertIn('nested.log', file_names)
        self.assertIn('config.ini', file_names)
        
        # 非递归搜索
        files = self.filter.find_files(recursive=False)
        file_names = [f.name for f in files]
        self.assertNotIn('nested.log', file_names)
        self.assertNotIn('config.ini', file_names)
    
    def test_combined_filters(self):
        """测试组合筛选条件"""
        # 查找扩展名为.txt且大小小于50字节的文件
        files = self.filter.find_files(
            extensions=['.txt'],
            max_size=50
        )
        self.assertEqual(len(files), 1)
        self.assertTrue(any(f.name == 'test1.txt' for f in files))
    
    def test_copy_files(self):
        """测试复制文件"""
        # 创建目标目录
        dest_dir = self.test_dir / 'copy_dest'
        
        # 查找并复制Python文件
        files = self.filter.find_files(extensions=['.py'])
        self.filter.copy_files(files, str(dest_dir))
        
        # 验证文件已复制
        self.assertTrue((dest_dir / 'test2.py').exists())
        
        # 验证原文件仍存在
        self.assertTrue((self.test_dir / 'test2.py').exists())
    
    def test_move_files(self):
        """测试移动文件"""
        # 创建目标目录
        dest_dir = self.test_dir / 'move_dest'
        
        # 查找并移动临时文件
        files = self.filter.find_files(extensions=['.tmp'])
        original_file = self.test_dir / 'small_file.tmp'
        self.assertTrue(original_file.exists())
        
        self.filter.move_files(files, str(dest_dir))
        
        # 验证文件已移动
        self.assertTrue((dest_dir / 'small_file.tmp').exists())
        
        # 验证原文件不存在
        self.assertFalse(original_file.exists())
    
    def test_delete_files(self):
        """测试删除文件"""
        # 查找并删除备份文件
        files = self.filter.find_files(extensions=['.bak'])
        original_file = self.test_dir / 'old_file.bak'
        self.assertTrue(original_file.exists())
        
        # 删除文件（不需要确认）
        self.filter.delete_files(files, confirm=False)
        
        # 验证文件已删除
        self.assertFalse(original_file.exists())

def run_tests():
    """运行测试"""
    unittest.main(verbosity=2)

if __name__ == '__main__':
    run_tests()
